generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Client {
  id   String @id @default(uuid())
  name String

  companyName String?
  tradingName String?
  cnpj        String?

  owners Owner[]

  brandAnniversary    DateTime?
  mainTypography      String?
  secondaryTypography String?

  importantDetails   String?
  restrictions       String?
  profileDescription String?
  segment            String[]
  services           String[]
  products           String[]

  storeAddress   Address? @relation("StoreAddress", fields: [storeAddressId], references: [id])
  storeAddressId String?  @unique

  factoryAddress   Address? @relation("FactoryAddress", fields: [factoryAddressId], references: [id])
  factoryAddressId String?  @unique

  paymentMethod String?
  paymentDay    Int?

  mission String?
  vision  String?
  values  String?

  urlLogoGoogleDrive String?

  createdAt           DateTime          @default(now())
  monthlyPostsLimit   Int?
  additionalContent   Int?
  instagramUsername   String?           @unique
  phone               String?
  monthlyStoriesLimit Int?
  archived            Boolean           @default(false)
  archivedAt          DateTime?
  monthlyPlannings    MonthlyPlanning[]
  resultsReport       ResultsReport[]
  generalDemands      GeneralDemand[]
  calendars           Calendar[]
}

model Owner {
  id        String    @id @default(uuid())
  name      String
  cpf       String?
  birthDate DateTime?

  rg            String?
  issuingAgency String?
  maritalStatus String?
  nationality   String? @default("Brazilian")
  profession    String?

  client   Client @relation(fields: [clientId], references: [id], onDelete: Cascade)
  clientId String

  address   Address? @relation("OwnerAddress", fields: [addressId], references: [id])
  addressId String?  @unique

  isPrimary Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([clientId])
}

model Address {
  id           String   @id @default(uuid())
  zipCode      String?
  street       String?
  number       String?
  neighborhood String?
  city         String?
  state        String?
  country      String?  @default("Brasil")
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  clientStore   Client? @relation("StoreAddress")
  clientFactory Client? @relation("FactoryAddress")
  owner         Owner?  @relation("OwnerAddress")
}

model Calendar {
  id          String         @id @default(cuid())
  name        String
  description String?
  color       String?        @default("#4285F4")
  client      Client         @relation(fields: [clientId], references: [id], onDelete: Cascade)
  clientId    String
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  events      Event[]
  holidays    HolidayEvent[]
  archived    Boolean        @default(false)

  @@index([clientId])
}

model Event {
  id          String    @id @default(cuid())
  title       String
  description String?
  startDate   DateTime
  endDate     DateTime?
  allDay      Boolean   @default(false)
  location    String?
  calendar    Calendar  @relation(fields: [calendarId], references: [id], onDelete: Cascade)
  calendarId  String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([calendarId])
  @@index([startDate])
}

model HolidayEvent {
  id          String   @id @default(cuid())
  title       String
  description String?
  month       Int
  day         Int?
  dayNotFixed String?
  color       String?  @default("#FF0000")
  allDay      Boolean  @default(true)
  isRecurring Boolean  @default(true)
  calendar    Calendar @relation(fields: [calendarId], references: [id], onDelete: Cascade)
  calendarId  String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([calendarId])
  @@index([month, day])
}

model MonthlyPlanning {
  id                          String           @id @default(uuid())
  clientId                    String
  month                       Int
  year                        Int
  status                      String           @default("pend. aprovação")
  showActivitiesInModuleThree Boolean          @default(false)
  createdAt                   DateTime         @default(now())
  updatedAt                   DateTime         @updatedAt
  client                      Client           @relation(fields: [clientId], references: [id])
  activities                  WeeklyActivity[]

  @@unique([clientId, month, year])
}

model WeeklyActivity {
  id                String          @id @default(uuid())
  monthlyPlanningId String
  week              Int
  description       String
  contents          Content[]
  monthlyPlanning   MonthlyPlanning @relation(fields: [monthlyPlanningId], references: [id])
}

model Content {
  id                  String         @id @default(cuid())
  weeklyActivityId    String
  activityDate        DateTime
  contentType         String
  channel             String?        @default("Feed")
  details             String?
  createdAt           DateTime       @default(now())
  destination         String
  caption             String?
  status              String?        @default("pendente")
  priority            String?        @default("normal")
  position            Int?           @default(9999)
  updatedAt           DateTime       @updatedAt
  copywriting         String?
  reference           String?
  urlStructuringFeed  String[]
  urlTypes            String[]       @default([])
  urlMediaTypes       String[]       @default([])
  urlThumbnails       String[]       @default([])
  urlFolder           String?
  review              String?
  assignedToId        String?
  carouselImagesCount Int?
  assignedTo          User?          @relation(fields: [assignedToId], references: [id])
  weeklyActivity      WeeklyActivity @relation(fields: [weeklyActivityId], references: [id], onDelete: Cascade)
  archived            Boolean        @default(false)

  steps ContentStep[]

  @@index([weeklyActivityId])
  @@index([assignedToId])
  @@index([position])
}

model ContentStep {
  id           String   @id @default(uuid())
  contentId    String
  type         StepType
  assignedToId String?
  assignedTo   User?    @relation(fields: [assignedToId], references: [id], onDelete: SetNull)
  content      Content  @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@unique([contentId, type])
  @@index([contentId])
  @@index([assignedToId])
}

enum StepType {
  CAPTACAO
  DESIGN
  EDICAO
  TRAFEGO
}

model ResultsReport {
  id        String   @id @default(uuid())
  clientId  String
  month     Int
  year      Int
  createdAt DateTime @default(now())
  updatedAt DateTime
  results   Result[]
  client    Client   @relation(fields: [clientId], references: [id])
}

model Result {
  id              String   @id @default(cuid())
  resultsReportId String
  createdAt       DateTime @default(now())

  //-------------------------//

  // Aba 1 - Métricas básicas ✅
  newFollowers      Int?
  totalPosts        Int?
  totalInteractions Int?
  totalStories      Int?
  totalViews        Int?

  //-------------------------//

  // Aba 2 - Visualizações ✅
  percentageAds          Float? // Percentual de anúncios
  percentageFollowers    Float? // Percentual de seguidores
  percentageNonFollowers Float? // Percentual de não seguidores

  accountsReached           Float? // Contas alcançadas (antes totalReach)
  percentageAccountsReached Float? // Percentual de contas alcançadas

  profileActivity           Float? // Atividade no perfil (número)
  percentageProfileActivity Float? // Atividade no perfil (percentual)

  profileVisits           Float? // Visitas ao perfil (número)
  percentageProfileVisits Float? // Visitas ao perfil (percentual)

  externalLinkTaps           Float? // Toques em links externos (número)
  percentageExternalLinkTaps Float? // Toques em links externos (percentual)

  businessAddressTaps           Float? // Toques no endereço comercial (número)
  percentageBusinessAddressTaps Float? // Toques no endereço comercial (percentual)

  //-------------------------//

  // Aba 3 - Interações ✅
  // Percentuais gerais de interações
  interactionsPercentageAds          Float? // Percentual de anúncios
  interactionsPercentageFollowers    Float? // Percentual de seguidores  
  interactionsPercentageNonFollowers Float? // Percentual de não seguidores

  // Reel
  reelInteractionPercentage Float? // Percentual de interação
  reelLikes                 Float?
  reelComments              Float?
  reelSaves                 Float?
  reelShares                Float?

  // Story
  storyInteractionPercentage Float? // Percentual de interação
  storyReplies               Float?

  // Publicações
  postInteractionPercentage Float? // Percentual de interação (renomeado de postInteractionRate)
  postLikes                 Float?
  postComments              Float?
  postSaves                 Float?
  postShares                Float?

  // Vídeos ao vivo
  liveInteractionPercentage Float? // Percentual de interação
  liveComments              Float?

  // Vídeos
  videoInteractionPercentage Float? // Percentual de interação
  videoLikes                 Float?
  videoComments              Float?
  videoSaves                 Float?
  videoShares                Float?

  //-------------------------//

  // Aba 4 - Seguidores ✅
  totalFollowers Int? // Total de seguidores
  unfollowCount  Int? // Deixaram de seguir

  // Localização dos seguidores (JSON com cidades e percentuais)
  followersLocation Json? // { "São Paulo": 45.2, "Rio de Janeiro": 23.1, ... }

  // Faixa etária dos seguidores (JSON com faixas e percentuais)
  followersAgeRange Json? // { "13-17": 5.2, "18-24": 35.1, "25-34": 28.5, ... }

  // Gênero dos seguidores (JSON com gêneros e percentuais)
  followersGender Json? // { "Masculino": 42.3, "Feminino": 56.7, "Outros": 1.0 }

  //-------------------------//

  // Dados aninhados (interações por tipo de conteúdo)
  interactionsByTypeContent Json?

  // Contagem de conteúdo - podem permanecer Int
  staticStories   Int?
  animatedStories Int?
  staticFeed      Int?
  reel            Int?
  extraMaterials  Int?

  // Totais gerais - podem ser Float para maior precisão
  interactionTotal Float?

  resultsReport ResultsReport @relation(fields: [resultsReportId], references: [id], onDelete: Cascade)

  @@index([resultsReportId])
}

model User {
  id                     String             @id @default(cuid())
  name                   String?
  email                  String             @unique
  emailVerified          DateTime?
  image                  String?
  fullName               String?
  hashedPassword         String?
  createdAt              DateTime           @default(now())
  updatedAt              DateTime           @updatedAt
  role                   Role               @default(VIEWER)
  accessLevel            AccessLevel        @default(VIEWER)
  address                String?
  birthDate              DateTime?
  hireDate               DateTime?
  CPF                    String?
  RG                     String?
  workCard               String?
  salary                 Float?
  children               Int?               @default(0)
  accounts               Account[]
  feedbacks              Feedback[]
  sessions               Session[]
  assignedContents       Content[]
  assignedGeneralDemands GeneralDemand[]
  ContentStep            ContentStep[]
  archived               Boolean            @default(false)
  workArrangement        WorkArrangement    @default(ON_SITE)
  PointRecord            PointRecord[]
  createdPointRecords    PointRecord[]      @relation("CreatedByUser")
  editedPointRecords     PointRecordAudit[] @relation("EditedByUser")
  excusedDays            ExcusedDay[]       @relation("ExcusedDayUser")
  createdExcusedDays     ExcusedDay[]       @relation("CreatedExcusedDays")
}

model PointRecord {
  id            String             @id @default(cuid())
  userId        String
  clockIn       DateTime?
  clockOut      DateTime?
  date          DateTime
  totalHours    Float?
  timeBank      Float?             @default(0)
  createdBy     String?
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  user          User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdByUser User?              @relation("CreatedByUser", fields: [createdBy], references: [id], onDelete: SetNull)
  auditLogs     PointRecordAudit[]

  @@index([userId])
  @@index([date])
  @@index([userId, date])
}

model PointRecordAudit {
  id            String      @id @default(cuid())
  pointRecordId String
  action        String
  field         String?
  oldValue      String?
  newValue      String?
  editedBy      String
  editedAt      DateTime    @default(now())
  pointRecord   PointRecord @relation(fields: [pointRecordId], references: [id], onDelete: Cascade)
  editedByUser  User        @relation("EditedByUser", fields: [editedBy], references: [id], onDelete: Cascade)

  @@index([pointRecordId])
  @@index([editedBy])
}

model ExcusedDay {
  id        String   @id @default(cuid())
  userId    String
  date      DateTime
  reason    String
  createdBy String
  createdAt DateTime @default(now())
  user      User     @relation("ExcusedDayUser", fields: [userId], references: [id], onDelete: Cascade)
  creator   User     @relation("CreatedExcusedDays", fields: [createdBy], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@index([userId])
  @@index([date])
}

model LooseClient {
  id        String          @id @default(cuid())
  name      String
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
  demands   GeneralDemand[]
}

model GeneralDemand {
  id                 String       @id @default(cuid())
  title              String
  description        String?
  dueDate            DateTime?
  status             String       @default("pendente")
  priority           String       @default("normal")
  position           Int?         @default(9999)
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt
  urlStructuringFeed String[]
  urlTypes           String[]     @default([])
  urlMediaTypes      String[]     @default([])
  urlThumbnails      String[]     @default([])
  urlFolder          String?
  clientId           String?
  looseClientId      String?
  assignedToId       String?
  client             Client?      @relation(fields: [clientId], references: [id])
  looseClient        LooseClient? @relation(fields: [looseClientId], references: [id])
  assignedTo         User?        @relation(fields: [assignedToId], references: [id])
  archived           Boolean      @default(false)
  review             String?

  @@index([clientId])
  @@index([looseClientId])
  @@index([assignedToId])
  @@index([position])
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

model Feedback {
  id          String         @id @default(cuid())
  title       String
  description String
  type        FeedbackType
  userEmail   String?
  userName    String?
  createdAt   DateTime       @default(now())
  status      FeedbackStatus @default(OPEN)
  user        User?          @relation(fields: [userEmail], references: [email])
}

model Notification {
  id         String    @id @default(cuid())
  content    String
  type       String
  entityId   String?
  entityType String?
  reference  String?
  isRead     Boolean   @default(false)
  createdAt  DateTime  @default(now())
  userId     String?
  importance String?   @default("normal")
  expiresAt  DateTime?
}

enum FeedbackType {
  SUGGESTION
  BUG
  QUESTION
}

enum FeedbackStatus {
  OPEN
  IN_PROGRESS
  CLOSED
}

enum Role {
  ADMIN
  VIEWER
  DEVELOPER
  COPY
  DESIGNER
  DESIGNER_SENIOR
  DESIGNER_JUNIOR
  GENERAL_ASSISTANT
}

enum AccessLevel {
  VIEWER
  EDITOR
}

enum WorkArrangement {
  ON_SITE
  REMOTE
  HYBRID
}
