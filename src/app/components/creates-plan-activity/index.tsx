import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>sis, CirclePlus, Sparkles } from "lucide-react";
import { Button } from "../ui/button";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "../ui/dialog";
import { Input } from "../ui/input";
import { toast } from "sonner";
import { Label } from "../ui/label";
import { Badge } from "../ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Checkbox } from "../ui/checkbox";
import { Textarea } from "../ui/textarea";
import { format } from "date-fns";
import { pt } from "date-fns/locale";
import { DatePicker } from "../ui/date-picker";
import { Switch } from "../ui/switch";

interface CreatePlanActivityProps {
    planningId: string;
    week?: number;
    planningMonth?: number;
    planningYear?: number;
    onSuccess?: () => void;
    onStartAdding?: () => void;
    onEndAdding?: () => void;
    disabled?: boolean;
}

const contentTypeOptions = [
    { value: "estático", label: "Estático" },
    { value: "vídeo", label: "Vídeo" },
    { value: "carrossel", label: "Carrossel" },
    { value: "animação", label: "Animação" },
];

const destinationOptions = [
    { value: "Story", label: "Story" },
    { value: "Feed", label: "Feed" },
    { value: "Story/Feed", label: "Story/Feed" },
];

const channelOptions = [
    { value: "Instagram", label: "Instagram" },
    { value: "Facebook", label: "Facebook" },
    { value: "YouTube", label: "YouTube" },
    { value: "TikTok", label: "TikTok" },
    { value: "Kwai", label: "Kwai" },
    { value: "Behance", label: "Behance" },
    { value: "Pinterest", label: "Pinterest" },
];

const stepTypeOptions = [
    { value: "CAPTACAO", label: "Captação" },
    { value: "DESIGN", label: "Design" },
    { value: "EDICAO", label: "Edição" },
    { value: "TRAFEGO", label: "Tráfego" },
];

interface User {
    id: string;
    name: string;
    email: string;
    image?: string;
    role?: string;
}

export const CreatePlanActivity = ({
    planningId,
    week: initialWeek,
    planningMonth,
    planningYear,
    onSuccess,
    onStartAdding,
    onEndAdding,
    forceOpen = false,
    disabled
}: CreatePlanActivityProps & { forceOpen?: boolean }) => {
    const [week, setWeek] = useState(initialWeek);
    const [selectedDate, setSelectedDate] = useState<Date | undefined>();
    const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
    const [contentType, setContentType] = useState<string>("");
    const [destination, setDestination] = useState<string>("");
    const [details, setDetails] = useState<string>("");
    const [caption, setCaption] = useState<string>("");
    const [copywriting, setCopywriting] = useState<string>("");
    const [reference, setReference] = useState<string>("");
    const [carouselImagesCount, setCarouselImagesCount] = useState<number>(1);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [limitInfo, setLimitInfo] = useState<{
        postsExceeded: boolean,
        storiesExceeded: boolean,
        currentPostCount: number,
        currentStoryCount: number,
        postLimit: number,
        storyLimit: number
    } | null>(null);

    const [limitWarning, setLimitWarning] = useState<{
        type: "posts" | "stories",
        limit: number,
        show: boolean
    } | null>(null);

    const [useMultipleSteps, setUseMultipleSteps] = useState<boolean>(false);
    const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
    const [users, setUsers] = useState<User[]>([]);
    const [steps, setSteps] = useState<Array<{ type: string, assignedToId: string }>>(
        stepTypeOptions.map(opt => ({
            type: opt.value,
            assignedToId: ''
        }))
    );

    const [keepModalOpen, setKeepModalOpen] = useState<boolean>(true);

    useEffect(() => {
        if (initialWeek) {
            setWeek(initialWeek);
        }
    }, [initialWeek]);

    useEffect(() => {
        if (forceOpen) {
            const timeout = setTimeout(() => checkPostLimit(planningId), 100);
            return () => clearTimeout(timeout);
        }
    }, [forceOpen, planningId]);

    useEffect(() => {
        if (!forceOpen) {
            setLimitInfo(null);
        }
    }, [forceOpen]);

    useEffect(() => {
        if (planningMonth && planningYear && !selectedDate) {
            const defaultDate = new Date(planningYear, planningMonth - 1, 15);
            setSelectedDate(defaultDate);
        }
    }, [planningMonth, planningYear, selectedDate]);

    useEffect(() => {
        if (planningMonth && planningYear && !selectedDate) {
            const dayOfMonth = (((initialWeek ?? 1) - 1) * 7) + 1;

            const plannedDate = new Date(planningYear, planningMonth - 1, dayOfMonth);

            setSelectedDate(plannedDate);
        }
    }, [planningMonth, planningYear, initialWeek, selectedDate]);

    useEffect(() => {
        if (forceOpen) {
            fetchUsers();
        }
    }, [forceOpen]);

    const fetchUsers = async () => {
        try {
            const response = await fetch('/api/users');
            if (response.ok) {
                const data = await response.json();
                const filteredUsers = data.filter((user: User) => user.role !== "DEVELOPER");
                setUsers(filteredUsers);
            } else {
                console.error('Erro ao carregar usuários');
            }
        } catch (error) {
            console.error('Erro ao carregar usuários:', error);
        }
    };

    const updateStepAssignee = (type: string, userId: string) => {
        setSteps(current =>
            current.map(step =>
                step.type === type ? { ...step, assignedToId: userId } : step
            )
        );
    };

    const getStepLabel = (type: string) => {
        const option = stepTypeOptions.find(opt => opt.value === type);
        return option ? option.label : type;
    };

    const checkPostLimit = async (planningId: string) => {
        try {
            setIsLoading(true);
            const response = await fetch(`/api/plannings?planningId=${planningId}`);
            const data = await response.json();

            setLimitInfo({
                postsExceeded: data.postsLimitExceeded,
                storiesExceeded: data.storiesLimitExceeded,
                currentPostCount: data.currentPostCount,
                currentStoryCount: data.currentStoryCount,
                postLimit: data.monthlyPostsLimit,
                storyLimit: data.monthlyStoriesLimit
            });

            return data.postsLimitExceeded || data.storiesLimitExceeded;
        } catch (error) {
            console.error("Erro ao verificar limites:", error);
            return false;
        } finally {
            setIsLoading(false);
        }
    };

    const handleSubmit = async (e: { preventDefault: () => void; }) => {
        e.preventDefault();

        if ((destination === "Feed" || destination === "Story/Feed") && limitInfo?.postsExceeded) {
            setLimitWarning({
                type: "posts",
                limit: limitInfo.postLimit,
                show: true
            });
            return;
        }

        const effectiveStoryCount = destination === "Story" && contentType === "carrossel"
            ? carouselImagesCount
            : 1;

        if ((destination === "Story" || destination === "Story/Feed") &&
            limitInfo?.storyLimit &&
            (limitInfo.currentStoryCount + effectiveStoryCount) > limitInfo.storyLimit) {
            setLimitWarning({
                type: "stories",
                limit: limitInfo.storyLimit,
                show: true
            });
            return;
        }

        submitActivity();
    };

    const handleContinueAnyway = () => {
        setLimitWarning(null);
        submitActivity();
    };

    const submitActivity = async () => {
        onStartAdding?.();
        setIsLoading(true);

        if (!selectedDate) {
            toast.error("Selecione uma data para a atividade");
            setIsLoading(false);
            return;
        }

        try {
            let newActivity;
            try {
                const activityResponse = await fetch("/api/activities", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                        monthlyPlanningId: planningId,
                        week: Number(week),
                        description: `Objetivo da semana ${week}`,
                    }),
                });

                if (!activityResponse.ok) {
                    const errorText = await activityResponse.text();
                    console.error(`Erro na resposta (${activityResponse.status}):`, errorText);
                    toast.error(`Erro ao criar atividade: ${activityResponse.statusText}`);
                    onEndAdding?.();
                    return;
                }

                const responseText = await activityResponse.text();
                if (!responseText.trim()) {
                    toast.error("Resposta vazia do servidor");
                    onEndAdding?.();
                    return;
                }

                newActivity = JSON.parse(responseText);
            } catch (activityError) {
                console.error("Erro ao criar atividade:", activityError);
                toast.error("Erro ao criar atividade");
                onEndAdding?.();
                return;
            } try {
                const validSteps = useMultipleSteps ? steps.filter(step => step.assignedToId && step.assignedToId.trim() !== '') : [];
                const hasAssignedUsers = useMultipleSteps ? validSteps.length > 0 : (selectedUserId !== null && selectedUserId !== '');

                const contentData = {
                    weeklyActivityId: newActivity.id,
                    activityDate: selectedDate?.toISOString(),
                    channel: selectedChannels.join(', '),
                    contentType: contentType,
                    destination: destination,
                    details: details,
                    copywriting: copywriting,
                    reference: reference,
                    caption: caption,
                    status: hasAssignedUsers ? "repassado" : "pendente",
                    carouselImagesCount: contentType === "carrossel" && destination === "Story" ? carouselImagesCount : undefined
                };

                if (useMultipleSteps) {
                    if (validSteps.length > 0) {
                        Object.assign(contentData, { steps: validSteps });
                    }
                } else if (selectedUserId) {
                    Object.assign(contentData, { assignedToId: selectedUserId });
                }

                const contentResponse = await fetch("/api/contents", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(contentData),
                });

                if (!contentResponse.ok) {
                    console.warn("Erro ao criar conteúdo, mas a atividade foi criada");
                    toast.error("Erro ao criar conteúdo");
                    onEndAdding?.();
                    return;
                }
            } catch (contentError) {
                console.error("Erro ao criar conteúdo:", contentError);
                toast.error("Erro ao criar conteúdo");
                onEndAdding?.();
                return;
            }

            setSelectedDate(undefined);
            setSelectedChannels([]);
            setContentType("");
            setDestination("");
            setDetails("");
            setCaption("");
            setCopywriting("");
            setReference("");
            setSelectedUserId(null);
            setSteps(stepTypeOptions.map(opt => ({
                type: opt.value,
                assignedToId: ''
            })));

            toast.success("Conteúdo cadastrado com sucesso!");

            if (keepModalOpen) {
                onSuccess?.();

                setTimeout(() => {
                    const dateField = document.getElementById("activityDate");
                    if (dateField) {
                        dateField.focus();
                    }
                }, 100);
            } else {
                onSuccess?.();
                onEndAdding?.();
            }

        } catch (error) {
            console.error("Erro geral:", error);
            toast.error("Ocorreu um erro inesperado");
            onEndAdding?.();
        } finally {
            setIsLoading(false);
        }
    };

    const handleOpen = (newState: boolean) => {
        if (newState) {
            onStartAdding?.();
        } else {
            onEndAdding?.();
        }
    };

    return (
        <Dialog
            open={forceOpen}
            onOpenChange={handleOpen}
        >
            <DialogTrigger asChild>
                <Button
                    disabled={disabled}
                    size="icon"
                    onClick={() => onStartAdding?.()}
                >
                    <CirclePlus size={16} />
                </Button>
            </DialogTrigger>

            {forceOpen && (
                <DialogContent key={initialWeek} autoFocus={false} className="max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>
                            Nova atividade
                            <Badge className="ml-2" variant="secondary">
                                Semana {week}
                            </Badge>
                        </DialogTitle>
                    </DialogHeader>

                    {isLoading ? (
                        <div className="border-blue-500 bg-blue-50 p-2 flex gap-2 items-center">
                            <Ellipsis size={20} className="text-blue-500 animate-pulse" />
                            <p className="text-sm text-blue-800">
                                Verificando limites de posts e stories
                            </p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-2 gap-2">
                            {limitInfo && (
                                <>
                                    <div className={`p-2 rounded flex gap-2 items-center ${limitInfo.postsExceeded ? "border-orange-500 bg-orange-50 dark:bg-orange-950" : "border-yellow-500 bg-yellow-50 dark:bg-yellow-950"}`}>
                                        <AlertTriangle size={16} className={limitInfo.postsExceeded ? "text-red-500" : "text-yellow-500"} />
                                        <p className={`text-xs ${limitInfo.postsExceeded ? "text-orange-800 dark:text-orange-200" : "text-yellow-800 dark:text-yellow-200"}`}>
                                            Posts: <strong>{limitInfo.currentPostCount}</strong>/{limitInfo.postLimit || "não definido"}
                                        </p>
                                    </div>
                                    <div className={`p-2 rounded flex gap-2 items-center ${limitInfo.postsExceeded ? "border-orange-500 bg-orange-50 dark:bg-orange-950" : "border-yellow-500 bg-yellow-50 dark:bg-yellow-950"}`}>
                                        <AlertTriangle size={16} className={limitInfo.storiesExceeded ? "text-red-500" : "text-yellow-500"} />
                                        <p className={`text-xs ${limitInfo.postsExceeded ? "text-orange-800 dark:text-orange-200" : "text-yellow-800 dark:text-yellow-200"}`}>
                                            Stories: <strong>{limitInfo.currentStoryCount}</strong>/{limitInfo.storyLimit || "não definido"}
                                        </p>
                                    </div>
                                </>
                            )}
                        </div>
                    )}

                    {limitWarning && (
                        <div className="border-orange-500 bg-orange-50 dark:bg-orange-950 p-2 text-sm">
                            <div className="text-orange-800 dark:text-orange-200">
                                Limite de {limitWarning.type === "posts" ? "posts" : "stories"} ({limitWarning.limit}) excedido.
                                Deseja continuar assim mesmo?
                            </div>
                        </div>
                    )}

                    <form onSubmit={handleSubmit} className="space-y-2">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            <div className="space-y-2">
                                <Label htmlFor="activityDate">Data</Label>
                                <DatePicker
                                    date={selectedDate}
                                    setDate={setSelectedDate}
                                    className="w-full"
                                    buttonLabel={selectedDate
                                        ? format(selectedDate, "dd 'de' MMMM 'de' yyyy", { locale: pt })
                                        : "Selecione uma data"
                                    }
                                    fromMonth={planningMonth && planningYear
                                        ? new Date(planningYear, planningMonth - 1, 1)
                                        : new Date(new Date().getFullYear(), new Date().getMonth() - 1)
                                    }
                                    toMonth={planningMonth && planningYear
                                        ? new Date(planningYear, planningMonth, 0)
                                        : new Date(new Date().getFullYear(), new Date().getMonth() + 1)
                                    }
                                    highlightWeek={true}
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="contentType">Tipo</Label>
                                <Select
                                    onValueChange={setContentType}
                                    value={contentType}
                                    required
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Selecione o tipo" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {contentTypeOptions.map(option => (
                                            <SelectItem key={option.value} value={option.value}>
                                                {option.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            <div className="space-y-2">
                                <Label htmlFor="channel">Canal</Label>
                                <div className="border rounded-md p-2">
                                    <Select
                                        onValueChange={(value) => {
                                            if (!selectedChannels.includes(value)) {
                                                setSelectedChannels([...selectedChannels, value]);
                                            }
                                        }}
                                        value=""
                                    >
                                        <SelectTrigger className="w-full mb-2">
                                            <SelectValue placeholder="Adicionar canal" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {channelOptions
                                                .filter(option => !selectedChannels.includes(option.value))
                                                .map(option => (
                                                    <SelectItem key={option.value} value={option.value}>
                                                        {option.label}
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>

                                    <div className="flex flex-wrap gap-1 mt-2">
                                        {selectedChannels.map(channel => (
                                            <Badge
                                                key={channel}
                                                variant="secondary"
                                                className="flex items-center gap-1"
                                            >
                                                {channel}
                                                <button
                                                    type="button"
                                                    onClick={() => setSelectedChannels(selectedChannels.filter(c => c !== channel))}
                                                    className="ml-1 hover:text-red-500 focus:outline-none"
                                                    aria-label={`Remover ${channel}`}
                                                >
                                                    ×
                                                </button>
                                            </Badge>
                                        ))}
                                        {selectedChannels.length === 0 && (
                                            <p className="text-sm text-gray-500">Nenhum canal selecionado</p>
                                        )}
                                    </div>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="destination">Destino</Label>
                                <Select
                                    onValueChange={setDestination}
                                    value={destination}
                                    required
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Selecione o destino" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {destinationOptions.map(option => (
                                            <SelectItem key={option.value} value={option.value}>
                                                {option.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        {contentType === "carrossel" && destination === "Story" && (
                            <div className="space-y-2 mt-3">
                                <Label htmlFor="carouselImagesCount">Quantidade de imagens</Label>
                                <Input
                                    id="carouselImagesCount"
                                    type="number"
                                    min="1"
                                    max="10"
                                    value={carouselImagesCount}
                                    onChange={(e) => setCarouselImagesCount(parseInt(e.target.value) || 1)}
                                    className="w-full"
                                />
                                <p className="text-xs text-muted-foreground">
                                    Cada imagem será contabilizada como um Story no limite mensal
                                </p>
                            </div>
                        )}

                        <div className="space-y-2 mt-3">
                            <Label htmlFor="details">Assunto</Label>
                            <Input
                                id="details"
                                placeholder="Digite o assunto"
                                value={details}
                                onChange={(e) => setDetails(e.target.value)}
                            />
                        </div>

                        <div className="space-y-2 mt-3">
                            <Label htmlFor="reference">Referência</Label>
                            <Input
                                id="reference"
                                placeholder="Digite a referência"
                                value={reference}
                                onChange={(e) => setReference(e.target.value)}
                            />
                        </div>

                        <div className="space-y-2 !mt-3">
                            <div className="flex gap-2">
                                <Label htmlFor="originalCaption">Legenda original (para otimizar com IA)</Label>
                                <p className="text-xs text-muted-foreground">Se você não deseja otimizar, preencha a legenda final manualmente.
                                </p>
                            </div>
                            <Textarea
                                id="originalCaption"
                                placeholder="Cole aqui a legenda se deseja otimizar com IA"
                                rows={8}
                            />
                            <div className="flex justify-end">
                                <Button
                                    type="button"
                                    size="sm"
                                    onClick={async () => {
                                        const originalCaption = (document.getElementById('originalCaption') as HTMLTextAreaElement)?.value;
                                        if (!originalCaption?.trim()) {
                                            toast.error('Preencha a legenda original primeiro');
                                            return;
                                        }
                                        setIsLoading(true);
                                        try {
                                            const response = await fetch('/api/generate-caption', {
                                                method: 'POST',
                                                headers: { 'Content-Type': 'application/json' },
                                                body: JSON.stringify({
                                                    originalCaption: originalCaption.trim()
                                                })
                                            });
                                            const data = await response.json();
                                            if (response.ok) {
                                                setCaption(data.caption);
                                                toast.success('Legenda otimizada com sucesso!');
                                            } else {
                                                toast.error(data.error || 'Erro ao otimizar legenda');
                                            }
                                        } catch (error) {
                                            toast.error('Erro ao otimizar legenda');
                                            console.error('Erro ao otimizar legenda:', error);
                                        } finally {
                                            setIsLoading(false);
                                        }
                                    }}
                                    disabled={isLoading}
                                    className="flex items-center gap-1 bg-gradient-to-r from-green-600 to-orange-600 text-white hover:from-green-700 hover:to-orange-700"
                                >
                                    <Sparkles size={14} />
                                    Otimizar com IA
                                </Button>
                            </div>
                        </div>

                        <div className="space-y-2 mt-3">
                            <div className="flex justify-between items-center">
                                <Label htmlFor="caption">Legenda final</Label>
                                <span className={`text-xs ${caption.length > 523 ? 'text-red-500' : 'text-gray-500'}`}>
                                    {caption.length}/523
                                </span>
                            </div>
                            <Textarea
                                id="caption"
                                placeholder="Legenda otimizada aparecerá aqui"
                                value={caption}
                                onChange={(e) => {
                                    const value = e.target.value.replace(/\n{2,}/g, '\n');
                                    if (value.length <= 523) {
                                        setCaption(value);
                                    }
                                }}
                                rows={8}
                            />
                            {caption.length > 500 && caption.length <= 523 && (
                                <p className="text-xs text-amber-600">
                                    Atenção: Você está próximo do limite de caracteres
                                </p>
                            )}

                            {caption.length > 523 && (
                                <p className="text-xs text-red-500">
                                    Atenção: Você excedeu o limite de caracteres
                                </p>
                            )}
                        </div>

                        <div className="space-y-2 mt-3">
                            <Label htmlFor="copywriting">Copy</Label>
                            <Textarea
                                id="copywriting"
                                placeholder="Digite o copy"
                                value={copywriting}
                                onChange={(e) => setCopywriting(e.target.value)}
                                rows={3}
                            />
                        </div>

                        <div className="border p-4 rounded-md space-y-4 mt-3">
                            <div className="flex items-center justify-between">
                                <Label htmlFor="useMultipleSteps" className="font-semibold">Atribuir responsáveis por etapas</Label>
                                <Switch
                                    id="useMultipleSteps"
                                    checked={useMultipleSteps}
                                    onCheckedChange={setUseMultipleSteps}
                                />
                            </div>

                            {!useMultipleSteps ? (
                                <div className="space-y-2">
                                    <Label htmlFor="assignedTo">Responsável</Label>
                                    <Select
                                        value={selectedUserId || ""}
                                        onValueChange={setSelectedUserId}
                                    >
                                        <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Selecione um responsável" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {users.map((user) => (
                                                <SelectItem key={user.id} value={user.id}>
                                                    <div className="flex items-center gap-2">
                                                        {user.name}
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    <p className="text-sm text-muted-foreground">Atribua responsáveis para cada etapa do conteúdo:</p>

                                    {steps.map((step) => (
                                        <div key={step.type} className="space-y-2">
                                            <Label htmlFor={`step-${step.type}`}>{getStepLabel(step.type)}</Label>
                                            <Select
                                                value={step.assignedToId}
                                                onValueChange={(value) => updateStepAssignee(step.type, value)}
                                            >
                                                <SelectTrigger className="w-full" id={`step-${step.type}`}>
                                                    <SelectValue placeholder="Selecione um responsável" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {users.map((user) => (
                                                        <SelectItem key={user.id} value={user.id}>
                                                            <div className="flex items-center gap-2">
                                                                {user.name}
                                                            </div>
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>

                        <div className="flex gap-2 items-center mb-2">
                            <Checkbox
                                id="keepOpen"
                                checked={keepModalOpen}
                                onCheckedChange={(checked) => setKeepModalOpen(!!checked)}
                            />
                            <label htmlFor="keepOpen" className="text-sm cursor-pointer">
                                Manter janela aberta para cadastros em sequência
                            </label>
                        </div>

                        <div className="flex gap-2 justify-end">
                            <Button type="button" variant="outline" onClick={() => onEndAdding?.()}>
                                Fechar
                            </Button>
                            {limitWarning ? (
                                <Button type="button" onClick={handleContinueAnyway}>
                                    Continuar mesmo asssim
                                </Button>
                            ) : (
                                <Button type="submit" disabled={isLoading}>
                                    {isLoading ? <Ellipsis /> : "Adicionar conteúdo"}
                                </Button>
                            )}
                        </div>
                    </form>
                </DialogContent>
            )}
        </Dialog>
    );
};