"use client";

import { <PERSON>, CardContent } from "../ui/card";
import { ChartNoAxesGantt, Grid3x3, ListTodo } from "lucide-react";
import { UserDemandsStats } from "../user-demands-stats";
import { UserLatestDemands } from "../user-latest-demands";
import { FeedStructuringTasks } from "../feed-structuring-tasks";

export function UserDashboard() {
  return (
    <div className="space-y-6 mt-6">
      <Card className="px-6 py-5">
        <CardContent className="m-0 p-0">
          <div className="flex gap-2 items-center mb-2">
            <ChartNoAxesGantt size={18} className="text-primary2" />
            <p className="text-sm font-medium">Estatísticas de demandas</p>
          </div>
          <UserDemandsStats />
        </CardContent>
      </Card>

      <Card className="px-6 py-5">
        <CardContent className="m-0 p-0">
          <div className="flex gap-2 items-center">
            <ListTodo size={18} className="text-primary2" />
            <p className="text-sm font-medium">Minhas demandas recentes</p>
          </div>
          <UserLatestDemands />
        </CardContent>
      </Card>

      <Card className="px-6 py-5">
        <CardContent className="m-0 p-0">
          <div className="flex justify-between items-center">
            <p className="text-sm font-medium">Estruturação do Feed</p>
            <Grid3x3 size={18} className="text-primary2" />
          </div>
          <FeedStructuringTasks />
        </CardContent>
      </Card>
    </div>
  );
}

