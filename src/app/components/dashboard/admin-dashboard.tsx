"use client";

import Link from "next/link";
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "../ui/card";
import { <PERSON><PERSON> } from "../ui/button";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "../ui/dialog";
import { Badge } from "../ui/badge";
import { Separator } from "../ui/separator";
import { CreatePlanning } from "../create-planning";
import { TotalPendingDemands } from "../total-pending-demands";
import { UserLatestDemands } from "../user-latest-demands";
import { ResultsReport } from "@prisma/client";
import { History, NotepadText, TrendingUp, Users, FileChartColumn, ListTodo } from "lucide-react";

interface MonthlyPlanning {
  id: string;
  month: string | number;
  year: number;
  createdAt?: string;
  clientName?: string;
  clientId?: string;
}

interface Client {
  id: string;
  name: string;
  monthlyPlannings?: MonthlyPlanning[];
  resultsReport?: ResultsReport[];
}

interface AdminDashboardProps {
  clients: Client[];
  allClients: Client[];
  currentMonthLabel: string;
  newsClients: Client[];
  formattedNewClientsPercentage: string;
  viewedClients: { id: string; name: string }[];
  onClientSelect: (clientId: string) => void;
  canCreatePlanning: boolean;
  onRefreshData: () => void;
}

export function AdminDashboard({
  clients,
  allClients,
  currentMonthLabel,
  newsClients,
  formattedNewClientsPercentage,
  viewedClients,
  onClientSelect,
  canCreatePlanning,
  onRefreshData,
}: AdminDashboardProps) {
  const [dialogOpen, setDialogOpen] = useState(false);

  const totalClients = clients.length;

  return (
    <>
      {/* Top 3 KPI cards */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-6">
        <Card className="px-6 py-5 h-40">
          <CardContent className="m-0 p-0 flex flex-col justify-between items-start h-full">
            <div className="flex items-start justify-between w-full">
              <div>
                <h3 className="text-sm">Total de clientes</h3>
                <span className="text-3xl font-bold">{totalClients}</span>
              </div>
              <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 flex items-center gap-1">
                <TrendingUp size={14} /> {formattedNewClientsPercentage}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              {newsClients.length > 0 ? `Novos clientes em ${currentMonthLabel}: ${newsClients.length}` : "Nenhum novo cliente este mês."}
            </p>
          </CardContent>
        </Card>

        <Card className="px-6 py-5 h-40">
          <CardContent className="m-0 p-0 flex flex-col justify-between items-end h-full">
            <div className="flex items-start justify-between w-full">
              <div>
                <h3 className="text-sm">Planejamentos criados em {currentMonthLabel}</h3>
                {clients.some(client => (client?.monthlyPlannings?.length || 0) > 0) ? (
                  <span className="text-3xl font-bold">{clients.reduce((total, client) => total + (client.monthlyPlannings || []).length, 0)}</span>
                ) : (
                  <span className="text-3xl font-bold">0</span>
                )}
              </div>
              <NotepadText size={18} className="text-primary2" />
            </div>
            {canCreatePlanning && (
              <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    Novo planejamento
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Novo planejamento</DialogTitle>
                  </DialogHeader>
                  <div onFocus={(e) => e.stopPropagation()}>
                    <CreatePlanning isShowTitle={false} onSuccess={onRefreshData} onClose={() => setDialogOpen(false)} />
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </CardContent>
        </Card>

        <Card className="px-6 py-5 h-40">
          <CardContent className="m-0 p-0 flex flex-col justify-between items-start h-full">
            <div className="flex items-start justify-between w-full">
              <div>
                <h3 className="text-sm">Relatórios de resultados criados em {currentMonthLabel}</h3>
                {clients.some(client => (client?.resultsReport?.length || 0) > 0) ? (
                  <span className="text-3xl font-bold">{clients.reduce((total, client) => total + (client.resultsReport || []).length, 0)}</span>
                ) : (
                  <span className="text-3xl font-bold">0</span>
                )}
              </div>
              <FileChartColumn size={18} className="text-primary2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 3 cards grid: recent viewed, latest plannings, latest results */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
        <Card className="px-6 py-5 h-40">
          <CardContent className="m-0 p-0 flex flex-col justify-between items-start h-full">
            <div className="flex items-start justify-between w-full">
              <div>
                <h3 className="text-sm">Clientes visualizados recentemente</h3>
                <ul className="mt-2">
                  {viewedClients.length > 0 ? (
                    viewedClients.map((client, index) => (
                      <li key={client.id ?? index} className="border-b text-sm py-1">
                        <button onClick={() => onClientSelect(client.id)} className="text-blue-600 hover:underline text-left w-full">
                          {client.name}
                        </button>
                      </li>
                    ))
                  ) : (
                    <p className="text-xs mt-8 text-zinc-500">Nenhum cliente visualizado recentemente.</p>
                  )}
                </ul>
              </div>
              <Users size={18} className="text-primary2" />
            </div>
          </CardContent>
        </Card>

        <Card className="px-6 py-5 h-40">
          <CardContent className="m-0 p-0 flex flex-col justify-between h-full">
            <div className="flex justify-between items-center">
              <p className="text-sm">Últimos planejamentos</p>
              <History size={18} className="text-primary2" />
            </div>
            {allClients.some(client => (client?.monthlyPlannings?.length || 0) > 0) ? (
              <ul className="mt-2">
                {allClients
                  .flatMap(client => (client.monthlyPlannings || []).map((planning: MonthlyPlanning) => ({
                    ...planning,
                    clientName: client.name,
                    clientId: client.id,
                  })))
                  .sort((a, b) => (b.createdAt ? new Date(b.createdAt).getTime() : 0) - (a.createdAt ? new Date(a.createdAt).getTime() : 0))
                  .slice(0, 5)
                  .map((planning) => (
                    <li key={planning.id} className="text-sm py-1">
                      <Link href={`/monthly-planning/${planning.clientId}`} className="text-blue-600 hover:underline border-b py-1">
                        {planning.clientName} - {planning.month}/{planning.year}
                      </Link>
                    </li>
                  ))}
              </ul>
            ) : (
              <p className="text-sm text-zinc-500">Nenhum planejamento criado.</p>
            )}
          </CardContent>
        </Card>

        <Card className="px-6 py-5 h-40">
          <CardContent className="m-0 p-0 flex flex-col h-full justify-between">
            <div className="flex justify-between items-center">
              <p className="text-sm">Últimos relatórios de resultados</p>
              <History size={18} className="text-primary2" />
            </div>
            {allClients.some(client => (client?.resultsReport?.length || 0) > 0) ? (
              <ul className="mt-2">
                {allClients
                  .flatMap(client => (client.resultsReport || []).map((report: ResultsReport & { createdAt?: string }) => ({
                    ...report,
                    clientName: client.name,
                    clientId: client.id,
                  })))
                  .sort((a, b) => (b.createdAt ? new Date(b.createdAt).getTime() : 0) - (a.createdAt ? new Date(a.createdAt).getTime() : 0))
                  .slice(0, 5)
                  .map((report) => (
                    <li key={report.id} className="text-sm py-1">
                      <Link href={`/results-report/${(report as any).clientId}`} className="text-blue-600 hover:underline border-b py-1">
                        {(report as any).clientName} - {report.month}/{report.year}
                      </Link>
                    </li>
                  ))}
              </ul>
            ) : (
              <p className="text-sm text-zinc-500">Nenhum relatório de resultados criado.</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Team demands and recent user demands */}
      <div className="space-y-6 mt-6">
        <div className="flex gap-2 items-center">
          <ListTodo size={18} className="text-primary2" />
          <p className="text-sm">Demandas do time</p>
        </div>
        <TotalPendingDemands />
        <Card className="px-6 py-5">
          <CardContent className="m-0 p-0">
            <div className="flex gap-2 items-center">
              <ListTodo size={18} className="text-primary2" />
              <p className="text-sm">Suas demandas recentes</p>
            </div>
            <UserLatestDemands />
          </CardContent>
        </Card>
      </div>
    </>
  );
}

