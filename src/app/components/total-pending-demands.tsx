"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "./ui/button"
import { Calendar, FileText, Zap } from "lucide-react"
import { Separator } from "./ui/separator"

export function TotalPendingDemands() {
    const [totalPendingDemands, setTotalPendingDemands] = useState<number | null>(null)
    const [totalPendingContentDemands, setTotalPendingContentDemands] = useState<number | null>(null)
    const [totalPendingGeneralDemands, setTotalPendingGeneralDemands] = useState<number | null>(null)
    const [currentMonth, setCurrentMonth] = useState<string>("")

    useEffect(() => {
        const monthNames = [
            "Janeiro",
            "Fevereiro",
            "Março",
            "Abril",
            "Maio",
            "Junho",
            "Julho",
            "Agosto",
            "Setembro",
            "Outubro",
            "Novembro",
            "Dezembro",
        ]
        const now = new Date()
        setCurrentMonth(monthNames[now.getMonth()])

        const fetchPendingDemands = async () => {
            try {
                const currentMonth = now.getMonth() + 1
                const currentYear = now.getFullYear()

                const response = await fetch(`/api/demands/pending-count?month=${currentMonth}&year=${currentYear}`)
                if (response.ok) {
                    const data = await response.json()
                    setTotalPendingDemands(data.count)
                    setTotalPendingContentDemands(data.contentDemands)
                    setTotalPendingGeneralDemands(data.generalDemands)
                } else {
                    console.error("Erro ao buscar demandas pendentes")
                    setTotalPendingDemands(0)
                    setTotalPendingContentDemands(0)
                    setTotalPendingGeneralDemands(0)
                }
            } catch (error) {
                console.error("Erro ao carregar demandas pendentes:", error)
                setTotalPendingDemands(0)
                setTotalPendingContentDemands(0)
                setTotalPendingGeneralDemands(0)
            }
        }

        fetchPendingDemands()
    }, [])

    if (totalPendingDemands === 0) {
        return (
            <div className="flex flex-col justify-center items-center py-16 px-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-zinc-900 dark:to-zinc-800 rounded-lg border">
                <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-lg font-medium text-muted-foreground">Nenhuma demanda pendente</p>
                <p className="text-sm text-muted-foreground">em {currentMonth}</p>
            </div>
        )
    }

    return (
        <div className="space-y-6 p-6 rounded-xl border !mt-2">
            <div className="flex gap-8 items-center">
                <div className="flex-1 space-y-6">
                    <div className="text-center space-y-2">
                        <div className="flex items-center justify-center gap-3">
                            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                                <Calendar className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                                <span className="text-4xl font-bold text-foreground">
                                    {totalPendingDemands !== null ? totalPendingDemands : "0"}
                                </span>
                            </div>
                        </div>
                        <p className="text-base font-medium text-muted-foreground">Demandas pendentes em {currentMonth}</p>
                    </div>

                    <Separator className="bg-border/50" />

                    <div className="grid grid-cols-2 gap-4">
                        <div className="bg-white dark:bg-zinc-800/50 p-4 rounded-lg border shadow-sm hover:shadow-md transition-shadow">
                            <div className="flex items-center gap-3 mb-2">
                                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                                    <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                </div>
                                <span className="text-2xl font-bold text-foreground">
                                    {totalPendingContentDemands !== null ? totalPendingContentDemands : "0"}
                                </span>
                            </div>
                            <p className="text-sm font-medium text-muted-foreground">Demandas de conteúdo</p>
                        </div>

                        <div className="bg-white dark:bg-zinc-800/50 p-4 rounded-lg border shadow-sm hover:shadow-md transition-shadow">
                            <div className="flex items-center gap-3 mb-2">
                                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full">
                                    <Zap className="h-4 w-4 text-green-600 dark:text-green-400" />
                                </div>
                                <span className="text-2xl font-bold text-foreground">
                                    {totalPendingGeneralDemands !== null ? totalPendingGeneralDemands : "0"}
                                </span>
                            </div>
                            <p className="text-sm font-medium text-muted-foreground">Demandas pontuais</p>
                        </div>
                    </div>
                </div>
            </div>

            <Separator className="bg-border/50" />

            <div className="flex justify-end pt-2">
                <Link href="/admin/demands">
                    <Button variant="secondary">
                        Ver todas as demandas
                    </Button>
                </Link>
            </div>
        </div>
    )
}
