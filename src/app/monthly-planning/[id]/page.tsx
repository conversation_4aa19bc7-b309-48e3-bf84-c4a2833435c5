"use client";

import { use<PERSON><PERSON><PERSON>, useEffect, use<PERSON>em<PERSON>, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Image from "next/image";
import { AppWindowMac, Calendar1, CirclePlus, Ellipsis, FileText, Gift, Info, MoveLeft, NotepadText, OctagonXIcon, PlusCircle } from "lucide-react";
import { Client as PrismaClient } from "@prisma/client";
import { Header } from "@/app/components/header";
import { Button } from "@/app/components/ui/button";
import { Footer } from "@/app/components/footer";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Badge } from "@/app/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog";
import { <PERSON>, CardContent, Card<PERSON>escription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/app/components/ui/card";
import { CreatePlanning } from "@/app/components/create-planning";
import Loading from "@/app/components/ui/loading";
import { toast } from "sonner";
import { Separator } from "@/app/components/ui/separator";
import { AboutPlanning } from "@/app/components/about-planning";
import { Input } from "@/app/components/ui/input";
import { CreatePlanActivity } from "@/app/components/creates-plan-activity";
import generatePDF from "@/app/components/generate-planning-pdf";
import Link from "next/link";
import React from 'react';
import { NotAllowed } from "@/app/components/not-allowed";
import { EditMonthlyWeek } from "@/app/components/edit-monthly-week";
import { WeeklyContentTable } from "@/app/components/weekly-content/table";
import { WeeklyContentMobileView } from "@/app/components/weekly-content/mobile-view";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/app/components/ui/dropdown-menu";
import { ClientNavigationModal } from "@/app/components/client-navigation-modal";
import { Switch } from "@/app/components/ui/switch";
import { HolidayEvent } from "@/types/calendar";

interface Client extends PrismaClient {
    monthlyPlannings: {
        id: string;
        month: string;
        year: number;
        monthlyPostsLimit: number;
        additionalContent: number;
        status: string;
        showActivitiesInModuleThree: boolean;
        activities: {
            id: string;
            description: string;
            week: number;
            contents: {
                id: string;
                activityDate: string;
                contentType: string;
                channel: string;
                details: string;
                destination: string;
                caption: string;
                status?: string;
                assignedTo?: {
                    id: string;
                    name: string;
                    email: string;
                    image?: string;
                } | null;
                steps?: {
                    id: string;
                    type: string;
                    assignedTo: {
                        id: string;
                        name: string;
                        email: string;
                        image?: string;
                    }
                }[];
            }[];
        }[];
    }[];
}

export default function MonthlyPlanningPage() {
    const { data: session, status } = useSession();
    const [client, setClient] = useState<Client | null>(null);
    const [isAdmin, setIsAdmin] = useState(false);
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [addingActivityToWeek, setAddingActivityToWeek] = useState<string | null>(null);
    const [newWeekNumber, setNewWeekNumber] = useState<string>("");
    const [newWeekDescription, setNewWeekDescription] = useState<string>("");
    const [isCreatingWeek, setIsCreatingWeek] = useState(false);
    const [updateKey, setUpdateKey] = useState(0);
    const [activeModals, setActiveModals] = useState<{ [key: string]: boolean }>({});
    const [editingWeeks, setEditingWeeks] = useState<Record<string, boolean>>({});
    const [clientNavigationOpen, setClientNavigationOpen] = useState(false);
    const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
    const [viewedClients, setViewedClients] = useState<{ id: string; name: string }[]>([]);
    const [holidays, setHolidays] = useState<HolidayEvent[]>([]);
    const [clientCalendars, setClientCalendars] = useState<Array<{ id: string, name: string }>>([]);
    const [isLoadingHolidays, setIsLoadingHolidays] = useState(false);

    const [userRole, setUserRole] = useState('');
    const [userAccessLevel, setUserAccessLevel] = useState('VIEWER');

    const monthNames = useMemo(() => [
        "Janeiro", "Fevereiro", "Março", "Abril",
        "Maio", "Junho", "Julho", "Agosto",
        "Setembro", "Outubro", "Novembro", "Dezembro",
    ], []);

    const [selectedMonth, setSelectedMonth] = useState<string | null>(() => {
        return new Date().toLocaleString("pt-BR", { month: "long" }).toLowerCase();
    });
    const [showCreatePlanning, setShowCreatePlanning] = useState(false);
    const router = useRouter();
    const { id } = useParams();

    const monthName = useCallback((month: number) => monthNames[month - 1], [monthNames]);

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    useEffect(() => {
        if (status === "authenticated" && id) {
            fetch(`/api/clients/${id}`)
                .then((res) => res.json())
                .then((data) => setClient(data))
                .catch((error) => console.error("Erro ao buscar cliente:", error));

            const fetchUserRole = async () => {
                try {
                    if (session?.user?.email) {
                        const response = await fetch(`/api/users/${session.user.email}`);
                        if (response.ok) {
                            const user = await response.json();

                            setUserRole(user?.role || '');
                            setUserAccessLevel(user?.accessLevel || 'VIEWER');

                            const allowedRoles = ["ADMIN", "DEVELOPER", "COPY", "DESIGNER_SENIOR", "GENERAL_ASSISTANT"];
                            setIsAdmin(allowedRoles.includes(user?.role || ''));
                        }
                    }
                } catch (error) {
                    console.error("Erro ao buscar função do usuário:", error);
                } finally {
                    setIsFetchingRole(false);
                }
            };

            fetchUserRole();
        }
    }, [status, id, session]);

    useEffect(() => {
        const storedClients = JSON.parse(localStorage.getItem("viewedClients") || "[]");
        setViewedClients(storedClients);
    }, []);

    useEffect(() => {
        if (client && !client.archived) {
            const viewedClients = JSON.parse(localStorage.getItem("viewedClients") || "[]");

            const updatedClients = [
                { id: client.id, name: client.name },
                ...viewedClients.filter((c: { id: string }) => c.id !== client.id),
            ].slice(0, 3);

            localStorage.setItem("viewedClients", JSON.stringify(updatedClients));
        }
    }, [client]);

    const refreshClientDataSilently = useCallback(async () => {
        if (id) {
            try {
                const timestamp = `_t=${Date.now()}`;

                await new Promise(resolve => setTimeout(resolve, 300));

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 15000);

                let attempts = 0;
                let success = false;
                let clientData = null;

                while (attempts < 3 && !success) {
                    try {
                        attempts++;

                        const res = await fetch(`/api/clients/${id}?${timestamp}&attempt=${attempts}`, {
                            cache: 'no-store',
                            headers: {
                                'Content-Type': 'application/json',
                                'Cache-Control': 'no-cache, no-store, must-revalidate',
                                'Pragma': 'no-cache',
                                'Expires': '0'
                            },
                            signal: controller.signal
                        });

                        if (!res.ok) {
                            throw new Error(`Erro na requisição: ${res.status}`);
                        }

                        clientData = await res.json();
                        success = true;
                    } catch (error: unknown) {
                        if (error instanceof Error && error.name === 'AbortError') {
                            break;
                        }

                        if (attempts < 3) {
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }
                    }
                }

                clearTimeout(timeoutId);

                if (success && clientData) {
                    setClient(clientData);
                    setUpdateKey(prev => prev + 1);
                } else {
                    toast.error("Falha ao atualizar dados. Tente novamente.");
                }
            } catch (error: unknown) {
                const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
                console.error("Erro ao atualizar dados do cliente:", errorMessage);
                toast.error("Erro ao atualizar dados");
            }
        }
    }, [id]);

    const refreshClientData = useCallback(() => {
        if (id) {
            setIsFetchingRole(true);
            fetch(`/api/clients/${id}`)
                .then((res) => res.json())
                .then((data) => {
                    setClient(data);
                })
                .catch((error: unknown) => {
                    const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
                    console.error("Erro ao atualizar dados do cliente:", errorMessage);
                })
                .finally(() => setIsFetchingRole(false));
        }
    }, [id]);

    useEffect(() => {
        if (selectedMonth && client?.monthlyPlannings) {
            const monthHasPlanning = client.monthlyPlannings.some(planning =>
                monthName(Number(planning.month)).toLowerCase() === selectedMonth
            );

            if (monthHasPlanning) {
                setShowCreatePlanning(false);
            }
        }
    }, [selectedMonth, client?.monthlyPlannings, monthName]);

    const createFirstWeekActivity = async (planningId: string) => {
        if (!newWeekNumber || !newWeekDescription) {
            toast.error("Preencha todos os campos!");
            return;
        }

        const weekNumber = parseInt(newWeekNumber);
        if (isNaN(weekNumber) || weekNumber < 1 || weekNumber > 5) {
            toast.error("O número da semana deve estar entre 1 e 5");
            return;
        }

        const currentPlanning = client?.monthlyPlannings?.find(p => p.id === planningId);

        if (currentPlanning) {
            const weekExists = currentPlanning.activities.some(activity => activity.week === weekNumber);

            if (weekExists) {
                toast.error(`A semana ${weekNumber} já existe neste planejamento`);
                return;
            }
        }

        try {
            setIsCreatingWeek(true);

            const response = await fetch("/api/activities", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    monthlyPlanningId: planningId,
                    week: weekNumber,
                    description: newWeekDescription
                }),
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || "Erro ao criar atividade");
            }

            toast.success("Atividade semanal criada com sucesso!");
            setNewWeekNumber("");
            setNewWeekDescription("");
            refreshClientData();
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
            console.error("Erro ao criar atividade:", errorMessage);
            toast.error(error instanceof Error ? error.message : "Ocorreu um erro ao criar a atividade");
        } finally {
            setIsCreatingWeek(false);
        }
    };

    const availableWeeks = (planningId: string) => {
        const planning = client?.monthlyPlannings?.find(p => p.id === planningId);
        if (!planning) return [1, 2, 3, 4, 5];

        const existingWeeks = planning.activities.map(activity => activity.week);
        return [1, 2, 3, 4, 5].filter(week => !existingWeeks.includes(week));
    };

    const handleGeneratePDF = (planningId: string) => {
        if (!client) return;
        generatePDF(client, planningId, monthName);
    };

    const updatePlanningStatus = async (planningId: string, status: string) => {
        try {
            const response = await fetch(`/api/monthly-planning/${planningId}/status`, {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ status }),
            });

            if (!response.ok) {
                throw new Error(`Erro ao atualizar status: ${response.status}`);
            }

            toast.success(`Status atualizado para "${status}"`);
            refreshClientData();
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
            console.error("Erro ao atualizar status do planejamento:", errorMessage);
            toast.error("Erro ao atualizar status do planejamento");
        }
    };

    const updateShowActivitiesInModuleThree = async (planningId: string, showActivities: boolean) => {
        try {
            const response = await fetch(`/api/monthly-planning/${planningId}/show-activities`, {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ showActivitiesInModuleThree: showActivities }),
            });

            if (!response.ok) {
                throw new Error(`Erro ao atualizar configuração: ${response.status}`);
            }

            if (showActivities) {
                toast.success(`Configuração ativada - as atividades com destino ao feed foram atualizadas para`);
            } else {
                toast.success(`Configuração de visualização atualizada`);
            }
            refreshClientData();
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
            console.error("Erro ao atualizar exibição de atividades:", errorMessage);
            toast.error("Erro ao atualizar configuração");
        }
    };

    const handleOpenClientNavigation = (clientId: string) => {
        setSelectedClientId(clientId);
        setClientNavigationOpen(true);
    };

    const fetchHolidaysForMonth = useCallback(async (calendarIds: string[]) => {
        if (!calendarIds.length || !selectedMonth) return;

        setIsLoadingHolidays(true);
        try {
            const monthIndex = monthNames.findIndex(
                m => m.toLowerCase() === selectedMonth.toLowerCase()
            );

            if (monthIndex === -1) return;

            const monthNumber = monthIndex + 1;

            const allHolidays: HolidayEvent[] = [];

            for (const calendarId of calendarIds) {
                const response = await fetch(`/api/holidays?calendarId=${calendarId}`);
                if (response.ok) {
                    const holidays = await response.json();
                    const monthHolidays = holidays.filter((h: HolidayEvent) => h.month === monthNumber);
                    allHolidays.push(...monthHolidays);
                }
            }

            allHolidays.sort((a, b) => {
                if (a.day !== undefined && a.day !== null && b.day !== undefined && b.day !== null) {
                    return a.day - b.day;
                }
                else if (a.day !== undefined && a.day !== null) {
                    return -1;
                }
                else if (b.day !== undefined && b.day !== null) {
                    return 1;
                }
                else if (a.dayNotFixed && b.dayNotFixed) {
                    return a.dayNotFixed.localeCompare(b.dayNotFixed);
                }
                return 0;
            });
            setHolidays(allHolidays);

        } catch (error) {
            console.error("Erro ao buscar eventos recorrentes:", error);
        } finally {
            setIsLoadingHolidays(false);
        }
    }, [selectedMonth, monthNames]);

    const fetchClientCalendars = useCallback(async () => {
        if (!id) return;

        try {
            const response = await fetch(`/api/calendars?clientId=${id}`);
            if (response.ok) {
                const calendars = await response.json();
                setClientCalendars(calendars.map((cal: { id: string, name: string }) => ({ id: cal.id, name: cal.name })));

                if (calendars.length > 0) {
                    await fetchHolidaysForMonth(calendars.map((cal: { id: string }) => cal.id));
                }
            }
        } catch (error) {
            console.error("Erro ao buscar calendários do cliente:", error);
        }
    }, [fetchHolidaysForMonth, id]);

    useEffect(() => {
        if (client) {
            fetchClientCalendars();
        }
    }, [client, fetchClientCalendars]);

    useEffect(() => {
        if (clientCalendars.length > 0) {
            fetchHolidaysForMonth(clientCalendars.map(cal => cal.id));
        }
    }, [selectedMonth, clientCalendars, fetchHolidaysForMonth]);

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:px-8 xs:pt-4 xs:pb-8 flex-grow">
                {isFetchingRole ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : !isAdmin ? (
                    <NotAllowed
                        page="clients"
                    />
                ) : !client ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : !client.id ? (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-1 pb-4">
                                <OctagonXIcon size={17} />
                                Cliente não encontrado
                            </CardTitle>
                            <Separator />
                            <CardDescription className="pt-4">
                                O cliente com o ID fornecido não foi encontrado.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="text-center flex gap-2 flex-col items-center justify-center h-[calc(100vh-20rem)]">
                            <Button variant="outline" onClick={() => router.push("/clients")}>
                                <MoveLeft /> Voltar
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="w-full">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <Button variant="outline" size="icon" onClick={() => router.push("/clients")}>
                                    <MoveLeft />
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() => handleOpenClientNavigation(client.id)}
                                    size="icon"
                                >
                                    <AppWindowMac />
                                </Button>
                            </div>
                            <ClientNavigationModal
                                open={clientNavigationOpen}
                                onOpenChange={setClientNavigationOpen}
                                clientId={selectedClientId}
                                clientName={viewedClients.find(client => client.id === selectedClientId)?.name}
                            />
                            <div className="flex items-center gap-3 group">
                                <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                    Planejamentos mensais
                                </h1>
                                <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                    <NotepadText size={24} color="#db5743" />
                                </div>
                            </div>
                        </div>
                        <h2 className="mt-4 text-lg font-semibold">{client.name}</h2>
                        <Link
                            href={`https://www.instagram.com/${client.instagramUsername}`}
                            target="_blank"
                            className="text-sm text-blue-500 hover:underline underline-offset-4 cursor-pointer inline-flex items-center gap-1"
                        >
                            <Image src="/instagram.svg" width={14} height={14} alt="Instagram" />
                            {client.instagramUsername}
                        </Link>
                        <div className="border-t pt-1 border-zinc-200 dark:border-zinc-800 mt-2">
                            <div className="flex flex-col xs:flex-row items-start xs:items-center gap-1 mb-2">
                                <h3 className="flex items-center gap-1 text-sm font-semibold">
                                    <NotepadText size={16} />
                                    Estratégia de conteúdo
                                </h3>
                                <div className="flex items-center gap-1">
                                    <Badge variant="secondary">
                                        etapa 2
                                    </Badge>
                                    <Badge variant="secondary">
                                        planejamento
                                    </Badge>
                                    <Dialog>
                                        <DialogTrigger asChild>
                                            <Button variant="link" size="icon">
                                                <Info />
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent className="sm:max-w-[550px] h-[85vh] p-0 overflow-y-auto">
                                            <div className="p-6">
                                                <DialogHeader>
                                                    <DialogTitle className="font-semibold text-gray-900 dark:text-gray-400">Saiba mais</DialogTitle>
                                                    <DialogDescription className="font-medium text-gray-700 dark:text-gray-200">
                                                        Etapa 2 — Planejamento
                                                        <span className="block mt-1 text-sm font-normal">Desenvolvimento da Estratégia de Conteúdo</span>
                                                    </DialogDescription>
                                                </DialogHeader>
                                                <AboutPlanning />
                                            </div>
                                        </DialogContent>
                                    </Dialog>
                                </div>
                            </div>
                            <Select onValueChange={(value) => setSelectedMonth(value)}>
                                <SelectTrigger className="sm:w-[180px]">
                                    <SelectValue placeholder="Escolha o mês" />
                                </SelectTrigger>
                                <SelectContent>
                                    {monthNames.map((name, index) => (
                                        <SelectItem key={index} value={name.toLowerCase()}>
                                            {name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        {client?.monthlyPlannings
                            ?.filter((planning) =>
                                selectedMonth ? monthName(Number(planning.month)).toLowerCase() === selectedMonth : true
                            )
                            .length === 0 ? (
                            <>
                                <p className="mt-4 text-sm text-gray-500">Nenhum planejamento encontrado para o mês selecionado</p>
                                <Separator className="mt-4" />
                                {!showCreatePlanning && (
                                    <div className="mt-8 flex flex-col items-center justify-center h-[calc(100vh-30rem)]">
                                        <Button
                                            disabled={userRole === 'COPY' && userAccessLevel === 'VIEWER'}
                                            variant="outline"
                                            onClick={() => {
                                                const monthHasPlanning = client?.monthlyPlannings?.some(planning =>
                                                    selectedMonth ?
                                                        monthName(Number(planning.month)).toLowerCase() === selectedMonth :
                                                        monthName(Number(planning.month)).toLowerCase() === monthNames[new Date().getMonth()].toLowerCase()
                                                );

                                                if (monthHasPlanning) {
                                                    toast.error("Já existe um planejamento cadastrado para este mês.");
                                                    return;
                                                }

                                                setShowCreatePlanning(true);
                                            }}>
                                            <CirclePlus />
                                            Adicionar planejamento
                                        </Button>
                                    </div>
                                )}
                            </>
                        ) : (
                            client?.monthlyPlannings
                                ?.filter((planning) =>
                                    selectedMonth ? monthName(Number(planning.month)).toLowerCase() === selectedMonth : true
                                )
                                .map((planning) => (
                                    <div key={planning.id} className="mt-4 text-sm">
                                        <div className="flex items-start justify-between">
                                            <p className="flex items-center gap-1 font-semibold">
                                                <Calendar1 size={16} />
                                                {`${monthName(Number(planning.month))} de ${planning.year}`}
                                            </p>

                                            <div className="flex flex-col items-end gap-4">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger>
                                                        <span
                                                            className={`${planning.status === "aprovado"
                                                                ? "bg-green-500 text-white"
                                                                : planning.status === "pend. aprovação"
                                                                    ? "bg-yellow-500 text-yellow-900"
                                                                    : "bg-red-500 text-white"
                                                                } p-2 rounded-xs cursor-pointer`}
                                                        >
                                                            {planning.status}
                                                        </span>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent className="mt-2 mr-7">
                                                        <DropdownMenuItem
                                                            onClick={() => updatePlanningStatus(planning.id, "pend. aprovação")}
                                                            disabled={userRole === 'COPY'}
                                                        >
                                                            pendente de aprovação
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            onClick={() => updatePlanningStatus(planning.id, "aprovado")}
                                                            disabled={userRole === 'COPY'}
                                                        >
                                                            aprovado
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            onClick={() => updatePlanningStatus(planning.id, "não aprovado")}
                                                            disabled={userRole === 'COPY'}
                                                        >
                                                            não aprovado
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                                <Button
                                                    onClick={() => handleGeneratePDF(planning.id)}
                                                >
                                                    <FileText />
                                                    Exportar PDF
                                                </Button>
                                            </div>
                                        </div>

                                        <div>
                                            <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2">
                                                <div className="flex flex-wrap gap-2 mt-2">
                                                    <div className="flex items-center gap-1">
                                                        <p>Número de posts:</p>
                                                        <Badge variant="outline">
                                                            {client.monthlyPostsLimit}
                                                        </Badge>
                                                    </div>
                                                    <div className="flex items-center gap-1">
                                                        <p>Número de stories:</p>
                                                        <Badge variant="outline">
                                                            {client.monthlyStoriesLimit}
                                                        </Badge>
                                                    </div>
                                                    <div className="flex items-center gap-1">
                                                        <p>Materiais extras:</p>
                                                        <Badge variant="outline">
                                                            {client.additionalContent}
                                                        </Badge>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex items-center mt-4 space-x-2">
                                                <Switch
                                                    id={`show-activities-${planning.id}`}
                                                    checked={planning.showActivitiesInModuleThree}
                                                    onCheckedChange={(checked) => updateShowActivitiesInModuleThree(planning.id, checked)}
                                                    disabled={userRole === 'COPY' && userAccessLevel === 'VIEWER'}
                                                />
                                                <span className="text-sm select-none cursor-default">
                                                    Exibir atividades no módulo de estruturação de feed
                                                </span>
                                            </div>

                                            {planning.activities.length > 0 && (
                                                <div className="mt-2 grid grid-cols-1 min-[1220px]:grid-cols-2 gap-4">
                                                    {Object.entries(
                                                        planning.activities.reduce((grouped, activity) => {
                                                            const weekNum = activity.week;
                                                            if (!grouped[weekNum]) {
                                                                grouped[weekNum] = [];
                                                            }
                                                            grouped[weekNum].push(activity);
                                                            return grouped;
                                                        }, {} as Record<number, typeof planning.activities>)
                                                    )
                                                        .sort(([weekA], [weekB]) => parseInt(weekA) - parseInt(weekB))
                                                        .map(([weekNum, activities]) => (
                                                            <Card key={`week-${weekNum}-${updateKey}`} className="w-full">
                                                                <CardHeader className="p-3 xs:p-6">
                                                                    <div className="flex items-center justify-between">
                                                                        <Separator className="w-2/3 my-4" />
                                                                        {!editingWeeks[weekNum] && (
                                                                            <CreatePlanActivity
                                                                                planningId={planning.id}
                                                                                week={parseInt(weekNum)}
                                                                                planningMonth={Number(planning.month)}
                                                                                planningYear={Number(planning.year)}
                                                                                onSuccess={refreshClientDataSilently}
                                                                                onStartAdding={() => {
                                                                                    setAddingActivityToWeek(weekNum);
                                                                                    setActiveModals((prev) => ({
                                                                                        ...prev,
                                                                                        [`week-${weekNum}`]: true
                                                                                    }));
                                                                                }}
                                                                                onEndAdding={() => {
                                                                                    setAddingActivityToWeek(null);
                                                                                    setActiveModals((prev) => ({
                                                                                        ...prev,
                                                                                        [`week-${weekNum}`]: false
                                                                                    }));
                                                                                }}
                                                                                disabled={userRole === 'COPY' && userAccessLevel === 'VIEWER'}
                                                                                forceOpen={activeModals[`week-${weekNum}`]}
                                                                            />
                                                                        )}
                                                                    </div>
                                                                    <div className="flex flex-col sm:flex-row items-start gap-2 mt-4">
                                                                        <EditMonthlyWeek
                                                                            activityId={activities[0].id}
                                                                            weekNum={weekNum}
                                                                            description={activities[0].description}
                                                                            onSuccess={refreshClientDataSilently}
                                                                            onEditingChange={(weekNum, isEditing) => {
                                                                                setEditingWeeks(prev => ({ ...prev, [weekNum]: isEditing }));
                                                                            }}
                                                                            disabled={userRole === 'COPY' && userAccessLevel === 'VIEWER'}
                                                                        />
                                                                        <div className={`${editingWeeks[weekNum] ? "hidden" : ""}`}>
                                                                            <CardTitle>{`Semana ${weekNum}`}</CardTitle>
                                                                            <CardDescription>
                                                                                {activities[0].description}
                                                                            </CardDescription>
                                                                        </div>
                                                                    </div>
                                                                </CardHeader>
                                                                <CardContent className="p-0 xs:p-6 w-full">
                                                                    <div className="hidden md:block">
                                                                        <WeeklyContentTable
                                                                            activities={activities}
                                                                            refreshClientData={refreshClientDataSilently}
                                                                            updateKey={updateKey}
                                                                            isReadOnly={userRole === 'COPY' && userAccessLevel === 'VIEWER'}
                                                                            userRole={userRole}
                                                                        />
                                                                    </div>

                                                                    <div className="md:hidden">
                                                                        <WeeklyContentMobileView
                                                                            activities={activities}
                                                                            refreshClientData={refreshClientDataSilently}
                                                                            updateKey={updateKey}
                                                                            isReadOnly={userRole === 'COPY' && userAccessLevel === 'VIEWER'}
                                                                            addingActivityToWeek={addingActivityToWeek}
                                                                            userRole={userRole}
                                                                        />
                                                                    </div>
                                                                </CardContent>
                                                            </Card>
                                                        ))}
                                                </div>
                                            )}
                                        </div>

                                        <div className="mt-4">
                                            <Card className="w-full">
                                                <CardHeader>
                                                    <CardTitle>Nova atividade semanal</CardTitle>
                                                    <CardDescription>Detalhes da semana</CardDescription>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="space-y-4">
                                                        <div className="flex flex-col xs:flex-row justify-center items-center gap-4">
                                                            <div className="space-y-2 w-full">
                                                                <label htmlFor="week-number" className="text-sm font-medium">
                                                                    Número da semana
                                                                </label>
                                                                <Select value={newWeekNumber} onValueChange={setNewWeekNumber}>
                                                                    <SelectTrigger>
                                                                        <SelectValue placeholder="Selecione a semana" />
                                                                    </SelectTrigger>
                                                                    <SelectContent>
                                                                        {availableWeeks(planning.id).length > 0 ? (
                                                                            availableWeeks(planning.id).map((week) => (
                                                                                <SelectItem key={week} value={String(week)}>
                                                                                    Semana {week}
                                                                                </SelectItem>
                                                                            ))
                                                                        ) : (
                                                                            <SelectItem value="Todas as semanas já criadas" disabled>
                                                                                Todas as semanas já foram criadas
                                                                            </SelectItem>
                                                                        )}
                                                                    </SelectContent>
                                                                </Select>
                                                            </div>
                                                            <div className="space-y-2 w-full">
                                                                <label htmlFor="activity-description" className="text-sm font-medium">
                                                                    Objetivo da semana
                                                                </label>
                                                                <Input
                                                                    id="activity-description"
                                                                    type="text"
                                                                    placeholder="Descreva o objetivo"
                                                                    className="w-full"
                                                                    value={newWeekDescription}
                                                                    onChange={(e) => setNewWeekDescription(e.target.value)}
                                                                />
                                                            </div>
                                                        </div>
                                                        <Button
                                                            className="w-full sm:w-auto"
                                                            onClick={() => createFirstWeekActivity(planning.id)}
                                                            disabled={isCreatingWeek || (userRole === 'COPY' && userAccessLevel === 'VIEWER')}
                                                        >
                                                            {isCreatingWeek ? (
                                                                <Ellipsis />
                                                            ) : (
                                                                <>
                                                                    <PlusCircle />
                                                                    Criar semana
                                                                </>
                                                            )}
                                                        </Button>

                                                        <div className="text-sm text-muted-foreground mt-2">
                                                            Adicione semanas de 1 a 5 para completar o planejamento mensal
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        </div>
                                        <div className="mt-6">
                                            <div className="flex items-center gap-2 mb-2">
                                                <Gift size={16} className="text-red-500" />
                                                <h3 className="text-md font-semibold">Datas comemorativas</h3>
                                            </div>

                                            {isLoadingHolidays ? (
                                                <Ellipsis className="mt-4 animate-pulse" />
                                            ) : holidays.length > 0 ? (
                                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                                    {holidays.map((holiday) => (
                                                        <div
                                                            key={holiday.id}
                                                            className="p-2 rounded-md border flex items-start gap-2"
                                                            style={{ borderLeftColor: holiday.color || '#FF0000', borderLeftWidth: '4px' }}
                                                        >
                                                            <div
                                                                className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"
                                                                style={{ backgroundColor: holiday.color || '#FF0000', color: '#FFFFFF' }}
                                                            >
                                                                {holiday.day}
                                                            </div>
                                                            <div>
                                                                <p className="font-medium">{holiday.title}</p>
                                                                {holiday.description && (
                                                                    <p className="text-sm text-muted-foreground">{holiday.description}</p>
                                                                )}
                                                                <p className="text-sm text-muted-foreground">
                                                                    {holiday.dayNotFixed}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            ) : (
                                                <div className="text-center p-4 text-muted-foreground">
                                                    Não há datas comemorativas cadastradas para este mês.
                                                    <Link href={`/clients/${id}/calendars`} className="text-blue-500 hover:underline ml-1">
                                                        Ir para Calendários.
                                                    </Link>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                ))
                        )}
                        {showCreatePlanning && (
                            <CreatePlanning
                                clientId={client.id}
                                clientName={client.name}
                                selectedMonth={selectedMonth}
                                selectedMonthName={monthName(selectedMonth ? monthNames.findIndex(m => m.toLowerCase() === selectedMonth) + 1 : new Date().getMonth() + 1)}
                                onClose={() => setShowCreatePlanning(false)}
                                isShowTitle={true}
                                onSuccess={refreshClientData}
                            />
                        )}
                    </div>
                )}
            </div>
            <Footer />
        </div>
    );
}
